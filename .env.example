# 数据库配置
# 开发环境示例
DATABASE_URL="mysql+pymysql://username:password@host:port/database"

# 生产环境示例
# DATABASE_URL="mysql+pymysql://username:password@host:port/database"

# NextAuth 配置
NEXTAUTH_SECRET="your-nextauth-secret-here-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# JWT 密钥
JWT_SECRET="your-jwt-secret-here-change-this-in-production"

# 应用配置
NODE_ENV="development"
LOG_LEVEL="info"

# 上传文件配置
UPLOAD_MAX_SIZE="10485760"  # 10MB
UPLOAD_ALLOWED_TYPES="xlsx,csv,pdf"

# Redis 配置 (可选)
REDIS_URL="redis://localhost:6379"

# 邮件配置 (可选)
SMTP_HOST=""
SMTP_PORT="587"
SMTP_USER=""
SMTP_PASS=""

# 应用版本
APP_VERSION="1.0.0"
APP_NAME="肺功能数据管理平台"