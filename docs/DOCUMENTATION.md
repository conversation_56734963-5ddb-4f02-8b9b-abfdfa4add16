# 肺功能数据管理平台 - 核心技术文档

本文档是肺功能数据管理平台的“技术白皮书”，包含了从产品设计到技术实现的全部核心信息，是开发和维护的主要参考。

## 目录

1.  [**产品设计 (Product Design)**](#1-产品设计-product-design)
    *   [1.1 项目概述](#11-项目概述)
    *   [1.2 功能设计](#12-功能设计)
    *   [1.3 界面与交互](#13-界面与交互)
2.  [**技术规范 (Technical Specification)**](#2-技术规范-technical-specification)
    *   [2.1 技术栈](#21-技术栈)
    *   [2.2 数据库设计](#22-数据库设计)
    *   [2.3 前端架构](#23-前端架构)
    *   [2.4 安全规范](#24-安全规范)
    *   [2.5 性能优化](#25-性能优化)
3.  [**API接口参考 (API Reference)**](#3-api接口参考-api-reference)
    *   [3.1 设计原则与格式](#31-设计原则与格式)
    *   [3.2 认证接口](#32-认证接口)
    *   [3.3 用户管理接口](#33-用户管理接口)
    *   [3.4 表单配置接口](#34-表单配置接口)
    *   [3.5 数据管理接口](#35-数据管理接口)
    *   [3.6 Webhook接口](#36-webhook接口)
    *   [3.7 健康检查接口](#37-健康检查接口)

---

## 1. 产品设计 (Product Design)

### 1.1 项目概述

**项目名称**: 肺功能数据管理平台 (Lung Function Data Management Platform)

**核心目标**: 构建一个现代化的数据管理平台，用于接收和处理来自“金数据”平台的Webhook数据，并提供完整的数据管理、用户管理、数据分析和导出功能。

**核心价值**:
- **自动化**: 自动完成数据收集、处理和入库。
- **可视化**: 提供直观、现代化的数据管理和配置界面。
- **灵活性**: 通过动态表单配置机制，适应不同类型的数据结构。
- **安全性**: 完整的用户认证、权限控制和操作审计。

### 1.2 功能设计

#### 用户认证系统
- **登录**: 用户名/密码登录，支持“记住我”。
- **安全**: 密码使用bcrypt加密，会话管理，登录失败限制。
- **用户管理**: 管理员可进行用户增删改查、重置密码、禁用/启用账户。
- **个人设置**: 用户可修改个人资料（昵称、邮箱、头像）和密码。

#### 表单配置管理
- **自动化流程**: 管理员粘贴一个表单的JSON样本 -> 系统自动解析字段 -> 管理员确认或调整字段映射 -> 系统自动创建对应的数据表。
- **动态表结构**: 每个表单类型对应一个独立的数据库表（`form_data_{form_id}`），包含通用字段和根据配置生成的动态字段。

#### Webhook数据接收
- **端点**: `/api/webhook/[form_id]`，根据表单ID动态接收。
- **处理流程**: 验证请求 -> 解析数据 -> 查找表单配置 -> 转换并存储数据 -> 记录日志。
- **错误处理**: 支持重试机制，记录详细错误日志，并完整保存原始JSON数据以备查。

#### 数据管理界面
- **展示**: 使用Ant Design表格进行分页、排序和筛选。
- **操作**: 支持完整的CRUD（创建、读取、更新、删除）操作。
- **高级功能**:
    - **批量操作**: 批量删除、批量更新。
    - **高级搜索**: 支持多字段、多条件组合搜索。
    - **状态记忆**: 自动保存用户的搜索和筛选条件。

#### 数据导出
- **格式**: 支持导出为Excel (.xlsx) 和 CSV (.csv)格式。
- **自定义**: 支持选择导出的字段和根据当前筛选条件导出。

### 1.3 界面与交互

- **设计风格**: 蓝色主题 (#1890ff)，遵循Ant Design设计规范，整体风格现代化、简洁。
- **布局**: 经典的“顶部导航 + 侧边栏菜单 + 主内容区”布局。
- **响应式**: 适配桌面、平板和移动设备。
- **交互反馈**: 提供清晰的加载状态、操作反馈（成功、失败、警告）和敏感操作的二次确认。

---

## 2. 技术规范 (Technical Specification)

### 2.1 技术栈

- **运行环境**: Node.js 18.x
- **核心框架**: Next.js 14.x (App Router)
- **数据库**: MySQL 8.0 (通过腾讯云轻量数据库实现)
- **ORM**: Prisma 5.x
- **UI库**: Ant Design 5.x + Tailwind CSS 3.x
- **认证**: NextAuth.js 4.x
- **状态管理**: Zustand 4.x
- **代码规范**: TypeScript, ESLint, Prettier

### 2.2 数据库设计

#### 核心表结构

1.  **`users` (用户表)**
    *   存储用户信息，如`username`, `password_hash`, `nickname`, `email`等。
    *   `username`字段唯一。

2.  **`form_configs` (表单配置表)**
    *   存储每个表单的配置信息，如`form_id`, `form_name`, `field_mapping` (JSON格式), `table_name`等。
    *   `form_id`字段唯一。

3.  **`system_logs` (系统日志表)**
    *   记录关键操作日志，如用户登录、数据修改、系统错误等。

#### 动态数据表

- 表名格式: `form_data_{form_id}` (例如: `form_data_ZFs2eo`)
- 包含固定字段 (`id`, `raw_data`, `created_at`等)和根据`field_mapping`动态生成的字段。
- 对常用查询字段（如姓名、电话）建立索引。

### 2.3 前端架构

- **目录结构**: 遵循Next.js App Router的最佳实践，将页面、组件、API、库函数、类型定义等清晰分离。
- **组件设计**: 采用原子化设计思想，分为基础UI组件、表单组件、数据展示组件和布局组件。
- **状态管理**:
    - **全局状态**: 使用Zustand管理用户认证信息、全局UI状态等。
    - **本地状态**: 使用React Hooks (`useState`, `useReducer`)管理组件内部状态。
- **数据请求**: 使用SWR或React Query进行API请求，实现缓存、自动刷新等功能。

### 2.4 安全规范

- **认证安全**:
    - 密码使用`bcryptjs`加盐哈希存储。
    - 使用`NextAuth.js`管理会话，Token存储在HttpOnly的Cookie中。
- **输入验证**:
    - 所有API的输入都使用`Zod`进行严格的模式验证。
    - 所有用户输入在前端和后端都进行校验。
- **访问控制**:
    - 使用Next.js中间件保护需要登录才能访问的页面和API。
- **防范常见漏洞**:
    - **SQL注入**: Prisma ORM从根本上防范。
    - **XSS**: Next.js和React默认对输出进行转义。
    - **CSRF**: NextAuth.js提供内置的CSRF保护。

### 2.5 性能优化

- **前端**:
    - **代码分割**: Next.js App Router自动实现。
    - **图片优化**: 使用`next/image`组件。
    - **懒加载**: 对非首屏组件进行懒加载。
- **后端**:
    - **数据库查询**: 优化Prisma查询，只选择必要的字段 (`select`)，使用分页 (`take`, `skip`)。
    - **缓存**: 对不经常变化的数据（如表单配置）进行内存缓存。
- **数据库**:
    - 为所有常用查询字段和外键建立索引。

---

## 3. API接口参考 (API Reference)

### 3.1 设计原则与格式

- **风格**: RESTful API
- **数据格式**: `application/json`
- **统一响应格式**:
    ```json
    // 成功
    {
      "success": true,
      "data": { ... },
      "timestamp": "2024-01-01T00:00:00Z"
    }

    // 失败
    {
      "success": false,
      "message": "错误描述",
      "code": "ERROR_CODE",
      "timestamp": "2024-01-01T00:00:00Z"
    }
    ```
- **限流**: 对关键接口（如登录）实施请求频率限制。

### 3.2 认证接口

- `POST /api/auth/signin`: 用户登录。
- `POST /api/auth/signout`: 用户登出。
- `GET /api/auth/me`: 获取当前登录用户信息。

### 3.3 用户管理接口

- `GET /api/users`: 获取用户列表（支持分页和搜索）。
- `POST /api/users`: 创建新用户。
- `PUT /api/users/{id}`: 更新用户信息。
- `DELETE /api/users/{id}`: 删除用户。
- `POST /api/users/{id}/reset-password`: 重置用户密码。
- `GET /api/user/profile`: 获取个人资料。
- `PUT /api/user/profile`: 更新个人资料。
- `PUT /api/user/password`: 修改密码。

### 3.4 表单配置接口

- `GET /api/forms`: 获取所有表单配置列表。
- `POST /api/forms`: 创建新的表单配置。
- `GET /api/forms/{formId}`: 获取单个表单配置详情。
- `PUT /api/forms/{formId}`: 更新表单配置。
- `DELETE /api/forms/{formId}`: 删除表单配置。

### 3.5 数据管理接口

- `GET /api/data/{formId}`: 获取指定表单的数据（支持分页、搜索、多条件筛选）。
- `DELETE /api/data/{formId}/batch`: 批量删除数据。
- `PUT /api/data/{formId}/batch`: 批量更新数据。
- `GET /api/data/{formId}/export`: 导出数据为Excel或CSV。

### 3.6 Webhook接口

- `POST /api/webhook/{formId}`: 接收金数据推送的数据。

### 3.7 健康检查接口

- `GET /api/health`: 检查系统健康状态，包括数据库连接、内存使用等。
