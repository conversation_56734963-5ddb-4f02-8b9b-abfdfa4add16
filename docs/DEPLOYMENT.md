# 肺功能数据管理平台 - 部署与运维手册

本文档是项目的“操作手册”，涵盖了从环境准备、自动化部署到日常维护和故障排查的所有内容。

## 🚀 快速部署指南

**适用于CentOS 7+/Ubuntu 20.04+系统的一键部署**

```bash
# 1. 克隆项目（或下载部署脚本）
git clone https://github.com/peckbyte/free_lung_function_project_admin.git
cd free_lung_function_project_admin

# 2. 运行一键部署脚本
chmod +x scripts/deploy-traditional.sh
./scripts/deploy-traditional.sh

# 3. 部署完成后，配置数据库连接
nano ~/www/wwwroot/free_lung_function_project/.env.production
# 修改 DATABASE_URL="mysql://用户名:密码@主机:端口/数据库名"

# 4. 运行数据库迁移
cd ~/www/wwwroot/free_lung_function_project
npx prisma db push

# 5. 重启应用
pm2 restart lung-function-admin

# 6. 访问应用
# http://your-server-ip:3011
```

**⚠️ 重要提醒**：部署脚本会自动处理大部分配置，但数据库连接信息需要手动配置。

## 目录

1.  [**环境要求**](#1-环境要求)
2.  [**部署流程**](#2-部署流程)
    *   [2.1 一键部署 (推荐)](#21-一键部署-推荐)
    *   [2.2 手动部署步骤 (高级用户)](#22-手动部署步骤高级用户)
    *   [2.3 部署后配置 (重要)](#23-部署后配置重要)
3.  [**进程管理 (PM2)**](#3-进程管理-pm2)
    *   [3.1 快速入门](#31-快速入门)
    *   [3.2 常用命令](#32-常用命令)
    *   [3.3 集群模式与内存管理](#33-集群模式与内存管理)
4.  [**日常维护**](#4-日常维护)
    *   [4.1 应用更新](#41-应用更新)
    *   [4.2 监控与日志](#42-监控与日志)
    *   [4.3 备份策略](#43-备份策略)
5.  [**故障排查 (Troubleshooting)**](#5-故障排查-troubleshooting)
    *   [5.1 应用启动问题](#51-应用启动问题)
    *   [5.2 数据库连接问题](#52-数据库连接问题)
    *   [5.3 Webhook接收问题](#53-webhook接收问题)
    *   [5.4 Nginx与网络问题](#54-nginx与网络问题)

---

## 1. 环境要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **磁盘**: 20GB 可用空间
- **操作系统**: CentOS 7+/Ubuntu 20.04+ (已测试兼容)

### 软件依赖
- **Node.js**: 16.x+ (CentOS 7自动安装16.x，其他系统安装18.x)
- **PM2**: 最新版本
- **数据库**: MySQL 8.0+
- **反向代理**: Nginx (推荐)
- **包管理器**: Yarn (自动安装)

---

## 2. 部署流程

### 2.1 一键部署 (推荐)

对于新服务器，推荐使用项目提供的一键部署脚本，它会自动完成环境准备、项目部署和服务配置。

#### 部署前准备
1. **创建普通用户**（不要使用root用户）
   ```bash
   # 如果使用root，先创建普通用户
   sudo useradd -m -s /bin/bash lungapp
   sudo usermod -aG sudo lungapp
   su - lungapp
   ```

2. **获取部署脚本**
   ```bash
   # 方法1: 直接克隆项目
   git clone https://github.com/peckbyte/free_lung_function_project_admin.git
   cd free_lung_function_project_admin

   # 方法2: 单独下载脚本
   wget https://raw.githubusercontent.com/peckbyte/free_lung_function_project_admin/main/scripts/deploy-traditional.sh
   ```

#### 执行部署
```bash
# 赋予执行权限
chmod +x scripts/deploy-traditional.sh

# 运行部署脚本
./scripts/deploy-traditional.sh
```

#### 脚本自动完成的任务
- ✅ 检查系统要求和用户权限
- ✅ 安装系统依赖（Node.js、Yarn、PM2、Nginx）
- ✅ 克隆项目代码到 `~/www/wwwroot/free_lung_function_project`
- ✅ 安装项目依赖并构建生产版本
- ✅ 配置PM2进程管理
- ✅ 配置Nginx反向代理
- ✅ 清理Docker相关文件（根据用户偏好）
- ✅ 检查配置并进行健康检查



### 2.2 手动部署步骤（高级用户）

如果您需要自定义部署过程，可以按照以下步骤手动部署：

1.  **环境准备**

    **Ubuntu系统**：
    ```bash
    # 更新系统
    sudo apt update && sudo apt upgrade -y

    # 安装Node.js 18
    curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
    sudo apt-get install -y nodejs

    # 安装Yarn、PM2和Nginx
    sudo npm install -g yarn pm2
    sudo apt install -y nginx
    ```

    **CentOS 7系统**：
    ```bash
    # 更新系统
    sudo yum update -y

    # 安装开发工具
    sudo yum groupinstall -y "Development Tools"
    sudo yum install -y curl wget git gcc gcc-c++ make

    # 安装EPEL仓库
    sudo yum install -y epel-release

    # 安装Node.js 16 (兼容CentOS 7)
    curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
    sudo yum install -y nodejs

    # 安装Yarn、PM2和Nginx
    sudo npm install -g yarn pm2
    sudo yum install -y nginx
    ```

2.  **项目部署**
    ```bash
    # 创建项目目录
    mkdir -p ~/www/wwwroot
    cd ~/www/wwwroot

    # 克隆项目
    git clone https://github.com/peckbyte/free_lung_function_project_admin.git free_lung_function_project
    cd free_lung_function_project

    # 安装依赖
    yarn install

    # 配置环境变量
    cp .env.example .env.production
    # 编辑 .env.production 配置数据库等信息

    # 生成Prisma客户端
    npx prisma generate

    # 构建项目
    yarn build
    ```

3.  **配置PM2**
    ```bash
    # 启动应用
    pm2 start ecosystem.config.js --env production

    # 保存配置
    pm2 save

    # 设置开机自启
    pm2 startup
    ```

4.  **配置Nginx**
    创建Nginx配置文件：
    ```bash
    sudo nano /etc/nginx/sites-available/lung-function-admin
    ```

    配置内容：
    ```nginx
    server {
        listen 80;
        server_name _;

        # 安全配置
        server_tokens off;
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # 静态文件
        location /_next/static/ {
            alias /home/<USER>/www/wwwroot/free_lung_function_project/.next/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        location /uploads/ {
            alias /home/<USER>/www/wwwroot/free_lung_function_project/public/uploads/;
            expires 30d;
            add_header Cache-Control "public";
        }

        # 主应用代理
        location / {
            proxy_pass http://127.0.0.1:3011;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        # 文件上传大小限制
        client_max_body_size 10M;

        # Gzip压缩
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_comp_level 6;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    }
    ```

    启用配置：
    ```bash
    sudo ln -sf /etc/nginx/sites-available/lung-function-admin /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    sudo nginx -t
    sudo systemctl reload nginx
    ```

5.  **SSL配置（可选）**
    ```bash
    sudo apt install certbot python3-certbot-nginx  # Ubuntu
    # 或
    sudo yum install certbot python2-certbot-nginx  # CentOS 7

    sudo certbot --nginx -d your-domain.com
    ```

### 2.3 部署后配置（重要）

部署脚本完成后，**必须**手动完成以下配置步骤：

#### 步骤1: 配置数据库连接
```bash
# 编辑生产环境配置文件
nano ~/www/wwwroot/free_lung_function_project/.env.production
```

**重要配置项**：
```env
# 应用配置（脚本已自动设置）
NODE_ENV=production
PORT=3011

# 数据库连接 - 必须修改为真实配置
DATABASE_URL="mysql://your_username:your_password@localhost:3306/lung_function_db"

# 认证密钥 - 必须修改为随机字符串
NEXTAUTH_URL="https://your-domain.com"  # 或 http://your-server-ip:3011
NEXTAUTH_SECRET="your-super-secret-key-for-nextauth"
JWT_SECRET="your-super-secret-key-for-jwt"

# 日志配置
LOG_LEVEL=info
LOG_FILE="/home/<USER>/www/wwwroot/free_lung_function_project/logs/production.log"
```

#### 步骤2: 运行数据库迁移
```bash
cd ~/www/wwwroot/free_lung_function_project
npx prisma db push
```

#### 步骤3: 重启应用
```bash
pm2 restart lung-function-admin
```

#### 步骤4: 创建管理员用户
```bash
cd ~/www/wwwroot/free_lung_function_project
node reset-password.js
```

#### 步骤5: 配置防火墙
**CentOS 7系统**：
```bash
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=3011/tcp
sudo firewall-cmd --reload
```

**Ubuntu系统**：
```bash
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3011
```

---

## 3. 进程管理 (PM2)

PM2是Node.js应用的守护进程管理器，能确保应用在后台持续运行，并提供日志管理、性能监控等功能。

### 3.1 快速入门

项目已通过 `ecosystem.config.js` 进行了预配置。

```bash
# 启动生产环境
pm2 start ecosystem.config.js --env production

# 保存配置，以便服务器重启后自动运行
pm2 save

# 设置开机自启
pm2 startup
```

### 3.2 常用命令

项目提供了简化的管理脚本 `scripts/pm2-manager.sh`。

```bash
# 查看状态
./scripts/pm2-manager.sh status

# 查看实时日志
./scripts/pm2-manager.sh logs

# 重启应用
./scripts/pm2-manager.sh restart

# 零停机重载 (用于更新)
./scripts/pm2-manager.sh reload
```

### 3.3 集群模式与内存管理

- **集群模式**: 为了充分利用多核CPU，可以启动集群模式。
  ```bash
  pm2 start ecosystem.config.js --env production-cluster
  ```
- **内存限制**: `ecosystem.config.js` 中已配置 `max_memory_restart: '1G'`，当应用内存超过1GB时会自动重启，防止内存泄漏导致服务器崩溃。

---

## 4. 日常维护

### 4.1 应用更新

#### 使用更新脚本（推荐）
```bash
# 使用项目提供的更新脚本
cd ~/www/wwwroot/free_lung_function_project
./scripts/update-deploy.sh
```

#### 手动更新步骤
```bash
# 1. 进入项目目录
cd ~/www/wwwroot/free_lung_function_project

# 2. 拉取最新代码
git pull origin main

# 3. 安装/更新依赖
yarn install

# 4. 重新构建项目
yarn build

# 5. 运行数据库迁移 (如果有模型变更)
npx prisma generate
npx prisma db push

# 6. 零停机重载应用
pm2 reload lung-function-admin
```

#### 验证更新
```bash
# 检查应用状态
pm2 status

# 查看应用日志
pm2 logs lung-function-admin

# 访问健康检查接口
curl http://localhost:3011/api/health
```

### 4.2 监控与日志

- **健康检查**: 访问 `https://your-domain.com/api/health` 查看系统健康状态。
- **实时日志**: `pm2 logs` 或 `tail -f /var/log/lung-function-admin.log`。
- **性能监控**: `pm2 monit` 提供了一个实时的命令行监控界面。

### 4.3 备份策略

- **数据库备份**: 使用`mysqldump`定期备份数据库。
- **文件备份**: 定期备份用户上传的文件目录（如 `uploads/`）。

---

## 5. 故障排查 (Troubleshooting)

### 5.1 应用启动问题

#### 症状：`pm2 status` 显示 `errored`
**排查步骤**：
1. **查看详细错误日志**：
   ```bash
   pm2 logs lung-function-admin --err
   pm2 describe lung-function-admin
   ```

2. **检查环境变量配置**：
   ```bash
   # 检查配置文件是否存在
   ls -la ~/www/wwwroot/free_lung_function_project/.env.production

   # 检查关键配置项
   grep -E "DATABASE_URL|PORT|NODE_ENV" ~/www/wwwroot/free_lung_function_project/.env.production
   ```

3. **检查端口占用**：
   ```bash
   sudo lsof -i :3011
   sudo netstat -tlnp | grep 3011
   ```

4. **手动测试启动**：
   ```bash
   cd ~/www/wwwroot/free_lung_function_project
   NODE_ENV=production PORT=3011 yarn start
   ```

#### 症状：应用启动后立即退出
**可能原因**：
- 数据库连接失败
- 环境变量配置错误
- 依赖包缺失

**解决方案**：
```bash
# 重新安装依赖
cd ~/www/wwwroot/free_lung_function_project
rm -rf node_modules
yarn install

# 重新生成Prisma客户端
npx prisma generate

# 重启应用
pm2 restart lung-function-admin
```

### 5.2 数据库连接问题

#### 症状：应用日志出现 "Can't connect to MySQL server"
**排查步骤**：
1. **验证数据库配置**：
   ```bash
   # 检查DATABASE_URL格式
   echo $DATABASE_URL
   # 正确格式：mysql://username:password@host:port/database
   ```

2. **测试数据库连接**：
   ```bash
   # 使用mysql客户端测试
   mysql -h your-db-host -P 3306 -u your-username -p your-database

   # 或使用telnet测试端口连通性
   telnet your-db-host 3306
   ```

3. **检查防火墙和安全组**：
   ```bash
   # CentOS 7检查防火墙
   sudo firewall-cmd --list-all

   # Ubuntu检查防火墙
   sudo ufw status
   ```

4. **使用Prisma测试连接**：
   ```bash
   cd ~/www/wwwroot/free_lung_function_project
   npx prisma db push --preview-feature
   ```

#### 症状：数据库连接间歇性失败
**可能原因**：
- 数据库连接池配置问题
- 网络不稳定
- 数据库服务器负载过高

**解决方案**：
在 `.env.production` 中添加连接池配置：
```env
DATABASE_URL="mysql://username:password@host:port/database?connection_limit=5&pool_timeout=20"
```

### 5.3 Webhook接收问题

- **症状**: 金数据后台提示推送失败，或数据未出现在平台中。
- **排查**: 
    1.  检查应用日志中是否有`webhook`相关的错误信息。
    2.  确认表单ID是否正确，以及该表单是否已在平台中配置并启用。
    3.  使用`curl`手动模拟金数据POST请求到您的Webhook URL，检查响应。

### 5.3 Webhook接收问题

- **症状**: 金数据后台提示推送失败，或数据未出现在平台中。
- **排查**:
    1.  检查应用日志中是否有`webhook`相关的错误信息。
    2.  确认表单ID是否正确，以及该表单是否已在平台中配置并启用。
    3.  使用`curl`手动模拟金数据POST请求到您的Webhook URL，检查响应。

### 5.4 Nginx与网络问题

#### 症状：访问域名出现 `502 Bad Gateway`
**排查步骤**：
1. **检查后端应用状态**：
   ```bash
   pm2 status
   pm2 logs lung-function-admin
   ```

2. **检查Nginx配置**：
   ```bash
   sudo nginx -t
   sudo systemctl status nginx
   ```

3. **检查端口连通性**：
   ```bash
   curl http://localhost:3011/api/health
   telnet localhost 3011
   ```

4. **查看Nginx错误日志**：
   ```bash
   sudo tail -f /var/log/nginx/error.log
   ```

#### 症状：静态文件无法访问
**可能原因**：
- Nginx配置中的静态文件路径错误
- 文件权限问题

**解决方案**：
```bash
# 检查静态文件路径
ls -la ~/www/wwwroot/free_lung_function_project/.next/static/
ls -la ~/www/wwwroot/free_lung_function_project/public/uploads/

# 修复文件权限
chmod -R 755 ~/www/wwwroot/free_lung_function_project/public/
```

---

## 📋 部署检查清单

### 部署前检查
- [ ] 服务器满足最低配置要求（2核4GB内存20GB磁盘）
- [ ] 使用普通用户（非root）进行部署
- [ ] 网络连接正常，可以访问GitHub和npm仓库
- [ ] 已准备好MySQL数据库连接信息

### 部署过程检查
- [ ] 部署脚本执行成功，无错误信息
- [ ] 项目代码已克隆到 `~/www/wwwroot/free_lung_function_project`
- [ ] 依赖安装成功，`node_modules` 目录存在
- [ ] 应用构建成功，`.next` 目录存在
- [ ] PM2进程启动成功，状态为 `online`
- [ ] Nginx配置正确，测试通过

### 部署后配置检查
- [ ] 已编辑 `.env.production` 文件，配置真实数据库连接
- [ ] 数据库迁移执行成功（`npx prisma db push`）
- [ ] 应用重启成功，PM2状态正常
- [ ] 防火墙已配置，端口80、443、3011已开放
- [ ] 健康检查接口正常响应（`/api/health`）

### 功能验证检查
- [ ] 可以通过浏览器访问应用首页
- [ ] 管理员用户创建成功，可以正常登录
- [ ] 数据库连接正常，可以查看数据
- [ ] 文件上传功能正常
- [ ] Webhook接收功能正常（如果需要）

### 安全检查
- [ ] 已修改默认的认证密钥（NEXTAUTH_SECRET、JWT_SECRET）
- [ ] 数据库用户权限最小化
- [ ] 已配置SSL证书（生产环境推荐）
- [ ] 已设置适当的文件权限

### 监控检查
- [ ] PM2开机自启已配置
- [ ] 日志文件正常写入
- [ ] 应用性能监控正常
- [ ] 备份策略已制定（如果需要）

---

## 📞 技术支持

如果在部署过程中遇到问题，可以：

1. **查看项目文档**：检查README.md和相关文档
2. **查看应用日志**：`pm2 logs lung-function-admin`
3. **检查系统日志**：`sudo journalctl -u nginx` 或 `sudo tail -f /var/log/nginx/error.log`
4. **提交Issue**：在GitHub项目页面提交详细的问题描述

**常用调试命令**：
```bash
# 检查系统状态
pm2 status
sudo systemctl status nginx
sudo systemctl status mysql  # 如果数据库在同一服务器

# 查看端口占用
sudo netstat -tlnp | grep -E "80|443|3011|3306"

# 测试网络连通性
curl -I http://localhost:3011
curl -I http://your-domain.com
```