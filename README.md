# 肺功能数据管理平台

<div align="center">

![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![Node](https://img.shields.io/badge/node-%3E%3D18.0.0-green.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

一个现代化的全栈数据管理平台，专为处理金数据表单提交、用户管理和数据分析而设计。

</div>

## 📖 项目文档

- **[🚀 快速上手 (Quick Start)](#-快速上手)**
- **[📄 核心技术文档 (Documentation)](docs/DOCUMENTATION.md)**
- **[⚙️ 部署与运维 (Deployment)](docs/DEPLOYMENT.md)**
- **[🤝 贡献指南 (Contributing)](CONTRIBUTING.md)**
- **[📅 更新日志 (Changelog)](CHANGELOG.md)**

---

## ✨ 项目概述

肺功能数据管理平台是一个用于接收和处理来自“金数据”等平台的Webhook数据，并提供完整的数据管理、用户管理和数据导出功能的Web应用。

### 核心价值

- **自动化**: 自动完成数据收集、处理和入库。
- **可视化**: 提供直观、现代化的数据管理和配置界面。
- **灵活性**: 通过动态表单配置机制，适应不同类型的数据结构。
- **安全性**: 完整的用户认证、权限控制和操作审计。

---

## 🛠️ 技术栈

- **核心框架**: Next.js 14 (App Router)
- **UI**: Ant Design 5.x + Tailwind CSS
- **数据库**: MySQL 8.0 + Prisma ORM
- **认证**: NextAuth.js
- **部署**: PM2 / Docker

---

## 🚀 快速上手

### 1. 环境要求

- Node.js 18.0+
- MySQL 8.0+
- npm 或 yarn

### 2. 本地开发步骤

```bash
# 1. 克隆项目
git clone https://github.com/peckbyte/free_lung_function_project_admin.git
cd free_lung_function_project_admin

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.local
# (根据.env.example的提示，编辑.env.local文件，填入数据库连接和密钥)

# 4. 初始化数据库
npx prisma db push

# 5. 启动开发服务器
npm run dev
```

应用将在 `http://localhost:3000` 上运行。默认管理员账户: `admin` / `admin123`。

### 3. 常用开发命令

```bash
# 运行测试
npm test

# 检查代码格式和类型
npm run lint
npm run type-check

# 打开Prisma Studio (数据库GUI)
npx prisma studio
```

---

## 🤝 如何贡献

我们欢迎所有形式的贡献！请查看 **[贡献指南](CONTRIBUTING.md)** 了解如何参与项目开发。

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。